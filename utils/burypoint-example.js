/**
 * 埋点使用示例
 * 展示如何在页面和组件中使用埋点方法
 */

import { reportPageView, reportClick, reportPageLeave, reportBuryPoint } from './utils.js';

// ==================== 页面中的使用示例 ====================

/**
 * 页面生命周期中的埋点使用
 */
const pageExample = {
  onLoad(options) {
    // 页面加载时发送 PV 埋点
    reportPageView('index_page', {
      from: options.from || 'direct',
      shopId: options.shopId || '',
      activityId: options.activityId || ''
    });
  },

  onShow() {
    // 页面显示时也可以发送埋点（如果需要）
    reportPageView('index_page_show');
  },

  onHide() {
    // 页面隐藏时发送离开埋点
    reportPageLeave('index_page');
  },

  onUnload() {
    // 页面卸载时发送离开埋点
    reportPageLeave('index_page_unload');
  },

  // 按钮点击事件
  onApplyButtonClick() {
    // 发送点击埋点
    reportClick('apply_button', {
      button_type: 'primary',
      position: 'center'
    });
    
    // 执行实际的业务逻辑
    this.handleApply();
  },

  // 商品点击事件
  onProductClick(e) {
    const { productId, productName } = e.currentTarget.dataset;
    
    reportClick('product_item', {
      product_id: productId,
      product_name: productName,
      position: e.currentTarget.dataset.index
    });
  },

  // 分享按钮点击
  onShareClick() {
    reportClick('share_button', {
      share_type: 'wechat'
    });
  }
};

// ==================== 组件中的使用示例 ====================

/**
 * 组件中的埋点使用
 */
const componentExample = {
  methods: {
    // 组件加载完成
    ready() {
      // 组件展示埋点
      reportPageView('popup_component', {
        component_type: 'success_popup'
      });
    },

    // 关闭按钮点击
    onClose() {
      reportClick('popup_close_button');
      this.triggerEvent('close');
    },

    // 确认按钮点击
    onConfirm() {
      reportClick('popup_confirm_button', {
        action: 'confirm'
      });
      this.triggerEvent('confirm');
    },

    // 跳转其他活动
    toOtherActivity() {
      reportClick('other_activity_button', {
        target_activity: 'new_user_gift',
        current_page: 'sorry_popup'
      });
      
      // 执行跳转逻辑...
    }
  }
};

// ==================== 特殊场景的埋点示例 ====================

/**
 * 表单提交埋点
 */
function reportFormSubmit(formData) {
  reportBuryPoint({
    e: 'submit',
    c: 'user_form',
    extra: {
      form_type: 'registration',
      fields_count: Object.keys(formData).length,
      has_phone: !!formData.phone,
      has_address: !!formData.address
    }
  });
}

/**
 * 错误埋点
 */
function reportError(error, context) {
  reportBuryPoint({
    e: 'error',
    c: 'system_error',
    extra: {
      error_type: error.name || 'unknown',
      error_message: error.message || '',
      error_context: context,
      timestamp: Date.now()
    }
  });
}

/**
 * 性能埋点
 */
function reportPerformance(actionName, duration) {
  reportBuryPoint({
    e: 'performance',
    c: actionName,
    extra: {
      duration: duration,
      timestamp: Date.now()
    }
  });
}

/**
 * 业务事件埋点
 */
function reportBusinessEvent(eventName, eventData) {
  reportBuryPoint({
    e: 'business',
    c: eventName,
    extra: eventData
  });
}

// ==================== 使用建议 ====================

/**
 * 埋点使用建议：
 * 
 * 1. 页面埋点：
 *    - onLoad: 使用 reportPageView() 发送 PV 埋点
 *    - onHide/onUnload: 使用 reportPageLeave() 发送离开埋点
 * 
 * 2. 点击埋点：
 *    - 所有重要按钮都应该添加 reportClick() 埋点
 *    - 传入有意义的元素标识和额外参数
 * 
 * 3. 业务埋点：
 *    - 关键业务流程节点使用 reportBuryPoint() 自定义埋点
 *    - 错误和异常情况也要埋点记录
 * 
 * 4. 参数规范：
 *    - c 参数：使用有意义的标识，如 'apply_button', 'product_list'
 *    - extra 参数：传入业务相关的上下文信息
 *    - 保持参数命名的一致性
 * 
 * 5. 注意事项：
 *    - 埋点失败不应影响主业务流程
 *    - 避免在循环中频繁发送埋点
 *    - 敏感信息不要放在埋点参数中
 */

export {
  pageExample,
  componentExample,
  reportFormSubmit,
  reportError,
  reportPerformance,
  reportBusinessEvent
};
