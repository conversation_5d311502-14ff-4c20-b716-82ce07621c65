import { httpRequest } from './http';

//活动操作前缀
const operationLogin = '/ausnutria/repurchase/userc';
const operation = '/ausnutria/repurchase';

export const lzLogin = (params) => {
  return httpRequest({
    operation: '登陆接口',
    url: `${operationLogin}/login`,
    method: 'POST',
    params,
  });
};

export const syncUser = (params) => {
  return httpRequest({
    operation: '同步用户信息',
    url: `${operationLogin}/syncUser`,
    method: 'POST',
    params,
  });
};

export const addNewMember = (params) => {
  return httpRequest({
    operation: '新增会员上报',
    url: `${operation}/addNewMember`,
    method: 'POST',
    params,
  });
};

export const getActivityInfo = (params) => {
  return httpRequest({
    operation: '活动详情',
    url: `${operation}/getActivityInfo`,
    method: 'POST',
    params,
  });
};

export const getActivitId = (params) => {
  return httpRequest({
    operation: '获取活动ID',
    url: `${operation}/getActivityId`,
    method: 'POST',
    params,
  });
};

export const applyPrize = (params) => {
  return httpRequest({
    operation: '报名参与',
    url: `${operation}/sign`,
    method: 'POST',
    params,
  });
};

export const receivePrize = (params) => {
  return httpRequest({
    operation: '领取奖品',
    url: `${operation}/receive`,
    method: 'POST',
    params,
  });
};

export const getSkuPages = (params) => {
  return httpRequest({
    operation: '获取曝光商品',
    url: `${operation}/getSkuPagesByType`,
    method: 'POST',
    params,
  });
};

// export const getUserQualification = (params) => {
//   return httpRequest({
//     operation: '获取用户资质',
//     url: `${operation}/getUserQualification`,
//     method: 'POST',
//     params,
//   });
// };

// export const checkPrizeStock = (params) => {
//   return httpRequest({
//     operation: '检查奖品库存',
//     url: `${operation}/checkPrizeStock`,
//     method: 'POST',
//     params,
//   });
// };

// export const sendCouponResult = (params) => {
//   return httpRequest({
//     operation: '上报发放优惠券结果',
//     url: `${operation}/sendCouponResult`,
//     method: 'POST',
//     params,
//   });
// };

// export const userReceiveGiftResult = (params) => {
//   return httpRequest({
//     operation: '用户是否领取过新客礼2和用户手机是否绑定过出生证号',
//     url: `${operation}/userReceiveGiftResult`,
//     method: 'POST',
//     params,
//   });
// };

export const getUserReceiveRecord = (params) => {
  return httpRequest({
    operation: '用户领取记录',
    url: `${operation}/getUserReceiveRecord`,
    method: 'POST',
    params,
  });
};

export const addAccessLog = (params) => {
  return httpRequest({
    operation: '埋点',
    url: `${operation}/addAccessLog`,
    method: 'POST',
    params,
  });
};
