import store from '../../store/store';

/**接口根路径 */
// TODO
// export const baseUrl = "https://dyminiapp.lucidata.cn/cjtd-c-web";
export const baseUrl = "https://cjtd-c-web-test.lucidata.cn/cjtd-c-web";
// export const baseUrl = "https://192.168.1.86:9998/cjtd-c-web";
// export const baseUrl = 'https://cjtd-c-web-test.lucidata.cn/cjtd-c-web';
// export const baseUrl = 'http://127.0.0.1:9998/cjtd-c-web';

export function httpRequest(model) {
  return new Promise((resolve, reject) => {
    tt.request({
      url: baseUrl + model.url,
      data: model.params,
      method: model.method,
      header: {
        'content-type': 'application/json;charset=UTF-8', // 默认值
        token: store.data.token,
      },
      dataType: 'json',
      success: function (res) {
        if (res.data.success) {
          resolve(res.data);
        } else {
          reject(res.data);
        }

        console.log('%c' + `【${model.operation}】:${model.url}`, 'color: ' + '#FF00FF', '\n入参==>', model.params, '\n出参==>', res.data);
      },
      fail: function (res) {
        console.warn('%c' + `【${model.operation}】:${model.url}`, 'color: ' + '#FF4500', '\n入参==>', model.params, '\n出参==>', res);
        reject(res);
      },
      complete: function (res) {},
    });
  });
}
