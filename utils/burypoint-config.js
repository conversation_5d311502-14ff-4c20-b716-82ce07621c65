/**
 * 埋点事件配置文件
 * 统一管理所有埋点事件的标识和参数
 */

// ==================== 页面埋点事件 ====================
export const PAGE_EVENTS = {
  // 首页
  INDEX_PAGE: 'index_page',
  INDEX_PAGE_SHOW: 'index_page_show',
  INDEX_PAGE_LEAVE: 'index_page_leave',
  
  // 活动页面
  ACTIVITY_PAGE: 'activity_page',
  ACTIVITY_PAGE_SHOW: 'activity_page_show',
  ACTIVITY_PAGE_LEAVE: 'activity_page_leave',
  
  // 结果页面
  RESULT_PAGE: 'result_page',
  SUCCESS_PAGE: 'success_page',
  FAIL_PAGE: 'fail_page'
};

// ==================== 组件埋点事件 ====================
export const COMPONENT_EVENTS = {
  // 弹窗组件
  SORRY_POPUP: 'sorry_popup',
  SUCCESS_POPUP: 'success_popup',
  MEMBER_POPUP: 'member_popup',
  RULE_POPUP: 'rule_popup',
  RECORD_POPUP: 'record_popup',
  ADDRESS_POPUP: 'address_popup',
  
  // 其他组件
  PRODUCT_LIST: 'product_list',
  BANNER_COMPONENT: 'banner_component'
};

// ==================== 点击事件 ====================
export const CLICK_EVENTS = {
  // 按钮点击
  APPLY_BUTTON: 'apply_button',
  CONFIRM_BUTTON: 'confirm_button',
  CANCEL_BUTTON: 'cancel_button',
  CLOSE_BUTTON: 'close_button',
  SHARE_BUTTON: 'share_button',
  OTHER_ACTIVITY_BUTTON: 'other_activity_button',
  
  // 导航点击
  TAB_CLICK: 'tab_click',
  MENU_CLICK: 'menu_click',
  BACK_BUTTON: 'back_button',
  
  // 商品相关
  PRODUCT_ITEM: 'product_item',
  PRODUCT_IMAGE: 'product_image',
  PRODUCT_DETAIL: 'product_detail',
  
  // 弹窗相关
  POPUP_CLOSE_BUTTON: 'popup_close_button',
  POPUP_CONFIRM_BUTTON: 'popup_confirm_button',
  POPUP_MASK: 'popup_mask',
  
  // 链接点击
  EXTERNAL_LINK: 'external_link',
  INTERNAL_LINK: 'internal_link'
};

// ==================== 业务事件 ====================
export const BUSINESS_EVENTS = {
  // 用户行为
  USER_LOGIN: 'user_login',
  USER_REGISTER: 'user_register',
  USER_LOGOUT: 'user_logout',
  
  // 活动相关
  ACTIVITY_APPLY: 'activity_apply',
  ACTIVITY_SUCCESS: 'activity_success',
  ACTIVITY_FAIL: 'activity_fail',
  
  // 表单提交
  FORM_SUBMIT: 'form_submit',
  FORM_VALIDATE_ERROR: 'form_validate_error',
  
  // 支付相关
  PAYMENT_START: 'payment_start',
  PAYMENT_SUCCESS: 'payment_success',
  PAYMENT_FAIL: 'payment_fail',
  
  // 分享相关
  SHARE_START: 'share_start',
  SHARE_SUCCESS: 'share_success',
  SHARE_CANCEL: 'share_cancel'
};

// ==================== 错误事件 ====================
export const ERROR_EVENTS = {
  // 系统错误
  SYSTEM_ERROR: 'system_error',
  NETWORK_ERROR: 'network_error',
  API_ERROR: 'api_error',
  
  // 业务错误
  BUSINESS_ERROR: 'business_error',
  VALIDATION_ERROR: 'validation_error',
  
  // 用户操作错误
  USER_ERROR: 'user_error',
  PERMISSION_ERROR: 'permission_error'
};

// ==================== 性能事件 ====================
export const PERFORMANCE_EVENTS = {
  PAGE_LOAD: 'page_load',
  API_RESPONSE: 'api_response',
  IMAGE_LOAD: 'image_load',
  COMPONENT_RENDER: 'component_render'
};

// ==================== 事件类型 ====================
export const EVENT_TYPES = {
  ENTER: 'enter',    // 进入/访问 (PV/UV)
  CLICK: 'click',    // 点击
  LEAVE: 'leave',    // 离开
  SUBMIT: 'submit',  // 提交
  ERROR: 'error',    // 错误
  BUSINESS: 'business', // 业务事件
  PERFORMANCE: 'performance' // 性能事件
};

// ==================== 按钮类型映射 ====================
export const BUTTON_TYPE_MAP = {
  1: 'shop_home',      // 跳店铺首页
  2: 'custom_page',    // 跳店铺自定义页
  3: 'new_user_gift_1', // 跳新客礼1.0
  4: 'new_user_gift_2', // 跳新客礼2.0
  5: 'collect_tank'    // 跳集罐
};

// ==================== 活动类型映射 ====================
export const ACTIVITY_TYPE_MAP = {
  'new_user': '01',
  'old_user': '02',
  'member': '03',
  'general': '99'
};

// ==================== 埋点参数模板 ====================
export const BURY_POINT_TEMPLATES = {
  // 页面访问模板
  PAGE_VIEW: {
    e: EVENT_TYPES.ENTER,
    c: '',
    extra: {
      page_type: '',
      from: '',
      timestamp: () => Date.now()
    }
  },
  
  // 点击事件模板
  CLICK: {
    e: EVENT_TYPES.CLICK,
    c: '',
    extra: {
      element_type: '',
      position: '',
      timestamp: () => Date.now()
    }
  },
  
  // 业务事件模板
  BUSINESS: {
    e: EVENT_TYPES.BUSINESS,
    c: '',
    extra: {
      business_type: '',
      result: '',
      timestamp: () => Date.now()
    }
  },
  
  // 错误事件模板
  ERROR: {
    e: EVENT_TYPES.ERROR,
    c: '',
    extra: {
      error_type: '',
      error_code: '',
      error_message: '',
      timestamp: () => Date.now()
    }
  }
};

// ==================== 工具函数 ====================

/**
 * 获取按钮类型名称
 * @param {number} btnType 按钮类型编号
 * @returns {string} 按钮类型名称
 */
export function getButtonTypeName(btnType) {
  return BUTTON_TYPE_MAP[btnType] || 'unknown';
}

/**
 * 获取活动类型编号
 * @param {string} activityType 活动类型名称
 * @returns {string} 活动类型编号
 */
export function getActivityTypeCode(activityType) {
  return ACTIVITY_TYPE_MAP[activityType] || '99';
}

/**
 * 创建埋点参数
 * @param {string} template 模板名称
 * @param {string} eventId 事件标识
 * @param {Object} extraParams 额外参数
 * @returns {Object} 埋点参数
 */
export function createBuryPointParams(template, eventId, extraParams = {}) {
  const templateData = BURY_POINT_TEMPLATES[template];
  if (!templateData) {
    console.warn('未找到埋点模板:', template);
    return null;
  }
  
  return {
    ...templateData,
    c: eventId,
    extra: {
      ...templateData.extra,
      ...extraParams,
      timestamp: Date.now()
    }
  };
}

// ==================== 导出所有配置 ====================
export default {
  PAGE_EVENTS,
  COMPONENT_EVENTS,
  CLICK_EVENTS,
  BUSINESS_EVENTS,
  ERROR_EVENTS,
  PERFORMANCE_EVENTS,
  EVENT_TYPES,
  BUTTON_TYPE_MAP,
  ACTIVITY_TYPE_MAP,
  BURY_POINT_TEMPLATES,
  getButtonTypeName,
  getActivityTypeCode,
  createBuryPointParams
};
