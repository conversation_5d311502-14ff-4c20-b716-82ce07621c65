# 抖音小程序埋点系统改造说明

## 改造概述

根据您提供的埋点接口和参数要求，我已经完成了抖音小程序埋点系统的全面改造，支持 PV、UV（enter）和点击（click）埋点。

## 主要改造内容

### 1. 核心埋点方法 (`utils/utils.js`)

#### 新增的主要方法：

- **`reportBuryPoint(options)`** - 统一埋点方法
- **`reportPageView(pageId, extra)`** - PV/UV 埋点
- **`reportClick(elementId, extra)`** - 点击埋点
- **`reportPageLeave(pageId, extra)`** - 离开页面埋点

#### 辅助方法：

- **`getAdSource()`** - 获取广告来源
- **`getCurrentPageUrl()`** - 获取当前页面路径
- **`getUserInfoForBury()`** - 获取用户信息用于埋点
- **`getActivityType()`** - 获取活动类型

### 2. 埋点配置管理 (`utils/burypoint-config.js`)

统一管理所有埋点事件标识和参数：

- **页面事件** (`PAGE_EVENTS`)
- **组件事件** (`COMPONENT_EVENTS`) 
- **点击事件** (`CLICK_EVENTS`)
- **业务事件** (`BUSINESS_EVENTS`)
- **错误事件** (`ERROR_EVENTS`)
- **性能事件** (`PERFORMANCE_EVENTS`)

### 3. 使用示例 (`utils/burypoint-example.js`)

提供了完整的使用示例，包括：

- 页面生命周期中的埋点使用
- 组件中的埋点使用
- 特殊场景的埋点示例
- 使用建议和注意事项

## 埋点参数结构

根据您提供的接口要求，埋点参数结构如下：

```javascript
{
  prd: 'crm',                    // 产品标识
  te: 'c',                       // 终端类型
  sid: store.data.shopId,        // 店铺ID
  uid: userInfo.uid,             // 用户ID
  t: '',                         // 时间标识
  opid: store.data.activityId,   // 活动ID
  ver: '1',                      // 版本
  c: eventId,                    // 具体内容标识
  e: eventType,                  // 事件类型 (enter/click/leave)
  l: '1',                        // 级别
  vid: '1',                      // 版本ID
  url: getCurrentPageUrl(),      // 当前页面URL
  ch: getAdSource(),             // 广告来源
  at: getActivityType()          // 活动类型
}
```

## 实际应用示例

### 1. 页面中的使用

```javascript
// pages/index/index.js
import { reportPageView, reportClick, reportPageLeave } from '../../utils/utils.js';
import { PAGE_EVENTS, CLICK_EVENTS } from '../../utils/burypoint-config.js';

// 页面加载
onLoad(options) {
  reportPageView(PAGE_EVENTS.INDEX_PAGE, {
    from: options.from || 'direct',
    shopId: options.shopId,
    activityId: options.activityId
  });
}

// 按钮点击
onApplyButtonClick() {
  reportClick(CLICK_EVENTS.APPLY_BUTTON, {
    button_type: 'apply',
    activity_status: store.data.actStatus
  });
}

// 页面离开
onHide() {
  reportPageLeave(PAGE_EVENTS.INDEX_PAGE_LEAVE);
}
```

### 2. 组件中的使用

```javascript
// components/sorry-pop/sorry-pop.js
import { reportPageView, reportClick } from '../../utils/utils.js';

// 组件展示
ready() {
  reportPageView('sorry_popup', {
    popup_type: 'apply_failed',
    click_type: store.clickType
  });
}

// 按钮点击
toOtherAct() {
  reportClick('other_activity_button', {
    btn_type: store.data.decoData.signFailBtnType0,
    shop_id: store.data.shopId
  });
}
```

## 支持的埋点类型

### 1. PV/UV 埋点 (enter)
- 页面访问埋点
- 组件展示埋点
- 使用 `reportPageView()` 方法

### 2. 点击埋点 (click)
- 按钮点击埋点
- 链接点击埋点
- 菜单点击埋点
- 使用 `reportClick()` 方法

### 3. 离开埋点 (leave)
- 页面离开埋点
- 组件关闭埋点
- 使用 `reportPageLeave()` 方法

## 特性优势

### 1. 兼容性
- 完全适配抖音小程序环境
- 移除了不兼容的 Web API（如 sessionStorage、window）
- 使用抖音小程序原生 API

### 2. 容错性
- 埋点失败不影响主业务流程
- 完善的错误处理机制
- 自动获取用户和页面信息

### 3. 可维护性
- 统一的配置管理
- 清晰的方法命名
- 完整的使用文档

### 4. 扩展性
- 支持自定义埋点事件
- 灵活的参数配置
- 易于添加新的埋点类型

## 使用建议

1. **页面埋点**：在 `onLoad` 中使用 `reportPageView()`
2. **点击埋点**：在所有重要按钮点击事件中使用 `reportClick()`
3. **离开埋点**：在 `onHide`/`onUnload` 中使用 `reportPageLeave()`
4. **参数规范**：使用有意义的事件标识和上下文参数
5. **性能考虑**：避免在循环中频繁发送埋点

## 注意事项

1. 埋点方法都是异步的，不会阻塞主流程
2. 敏感信息不要放在埋点参数中
3. 保持事件标识的一致性和可读性
4. 定期检查埋点数据的准确性

通过这次改造，您的抖音小程序现在拥有了完整、规范、易用的埋点系统，能够准确追踪用户行为和页面访问情况。
