import create from 'mini-stores';
import { httpRequest } from '../../utils/constants/http';
const store = require('../../store/store');
const stores = {
  $store: store, // axml视图上使用$store.xxx即对应store.data.xxx的值。
};
const app = getApp();

create.Component(stores, {
  data: {
    showRegionPicker: false, // 控制地区选择器的显示与隐藏
    realName: "", // 姓名
    mobile: "", // 手机号
    region: ["", "", ""], // 地区
    addressDetail: "", // 详细地址
    hasFinished: true, // 是否已完成
  },
  properties: {},
  created() {},
  onLoad () {
    this.hasFinished = store.data.hasFinished;
  },
  methods: {
    onReceiverInput(e) {
      this.setData({ realName: e.detail.value });
    },
    
    onMobileInput(e) {
      this.setData({ mobile: e.detail.value });
    },
    
    onDetailInput(e) {
      this.setData({ addressDetail: e.detail.value });
    },
      // 显示地区选择器
    showRegionPicker() {
      this.setData({ showRegionPicker: true });
    },
    // 省市区选择变化事件
    bindRegionChange: function(e) {
      this.setData({
        region: e.detail.value
      });
    },
    // 检查表单
    checkForm () {
      const phone = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
      console.log(this.data);
      if (!this.data.realName || this.data.realName === '') {
        tt.showToast({
          title: '请输入姓名',
          icon: 'none',
        });
      } else if (!this.data.mobile || this.data.mobile === '') {
        tt.showToast({
          title: '请输入手机号',
          icon: 'none',
        });
      } else if (!phone.test(this.data.mobile)) {
        tt.showToast({
          title: '请输入正确的手机号',
          icon: 'none',
        });
      } else if (this.data.region[0] === '' || this.data.region[1] === '' || this.data.region[2] === '') {
        tt.showToast({
          title: '请选择省市区',
          icon: 'none',
        });
      } else if (!this.data.addressDetail || this.data.addressDetail === '') {
        tt.showToast({
          title: '请输入详细地址',
          icon: 'none',
        });
      } else {
        this.submit();
      }
    },
    submit () {
      // TODO
      // 提交表单
      store.setAddressForm(
        {
          realName: this.data.realName, // 姓名
          mobile: this.data.mobile, // 手机号
          region: this.data.region, // 地址-省市区
          addressDetail: this.data.addressDetail, // 详细地址
        }
      );
      try {
        saveAddress({
          realName: this.data.realName, // 姓名
          mobile: this.data.mobile, // 手机号
          region: this.data.region, // 地址-省市区
          addressDetail: this.data.addressDetail, // 详细地址
        });
      } catch (error) {
        showToast(error.message);
      }
      console.log('提交表单', store.data.addressForm);
      // 关闭当前弹窗
      this.triggerEvent('close');
    },
    onClose() {
      this.triggerEvent('close');
    },
  },

});
