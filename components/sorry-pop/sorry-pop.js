import create from 'mini-stores';
const store = require('../../store/store');
import { openEcShop, reportPageView, reportClick } from '../../utils/utils';
const stores = {
  $store: store, // axml视图上使用$store.xxx即对应store.data.xxx的值。
};
const app = getApp();

create.Component(stores, {
  data: {
    shopMap: [
      {
        // 海普诺凯1897官方旗舰店
        shopId: '37702451',
        new1Appid: 'tt021b74a69d10a04b01',
        new2Appid: 'tt27d3516eb8d8de5a01',
        collectTankAppid: 'ttae1ad8a731139edc01',
      },
      {
        // 能立多旗舰店
        shopId: '40842709',
        new1Appid: 'tt31cc4771aec78d7f01',
        new2Appid: 'ttc58ab3375a813ce101',
        collectTankAppid: 'ttdbe9ad5444e0e9e801',
      },
      {
        // 佳贝艾特KABRITA官方旗舰店
        shopId: '407961',
        new1Appid: 'tt5e0028bdc10bf98e01',
        new2Appid: 'tt77390184e8328e9f01',
        collectTankAppid: '',
      },
      {
        // 佳贝艾特母婴用品旗舰店
        shopId: '38566226',
        new1Appid: 'tte3e6955b7ced460101',
        new2Appid: 'tt746f02d2eb648d2901',
        collectTankAppid: '',
      },
      {
        // 佳贝艾特成人奶粉旗舰店
        shopId: '99250902',
        new1Appid: 'tt6e991824f192cad601',
        new2Appid: 'tta4d2d26f3bd95dd301',
        collectTankAppid: '',
      },
      {
        // 佳贝艾特营养奶粉旗舰店
        shopId: '125644821',
        new1Appid: 'tta14fd7f23e44056401',
        new2Appid: 'tt0422729f9b29fd0501',
        collectTankAppid: '',
      },
      {
        // 佳贝艾特婴童用品旗舰店
        shopId: '169039348',
        new1Appid: 'ttce39b88451326a4c01',
        new2Appid: 'ttd759a1e448522e9501',
        collectTankAppid: '',
      },
      {
        // 佳贝艾特母婴旗舰店
        shopId: '62361844',
        new1Appid: 'tt5ee52858c1eefabb01',
        new2Appid: 'tt94b2e20f0bb96d6201',
        collectTankAppid: '',
      },
    ]
  },
  properties: {},
  created() {
    // 组件展示埋点
    reportPageView('enter');
  },
  methods: {
    // 去参加其他活动跳转
    toOtherAct() {
      // 发送点击埋点
      reportClick('qcjqthd');

      // 根据点击类型获取对应的配置
      const isApplyFail = store.data.clickType === 0; // 报名失败
      const isReceiveFail = store.data.clickType === 1; // 领取奖品失败
      console.log(isApplyFail, isReceiveFail);

      if (!isApplyFail && !isReceiveFail) return;

      const btnType = isApplyFail ? store.data.decoData.signFailBtnType0 : store.data.decoData.signFailBtnType1;
      const btnId = isApplyFail ? store.data.decoData.signFailBtnId0 : store.data.decoData.signFailBtnId1;

      this.handleJumpByType(btnType, btnId);
    },

    // 根据按钮类型处理跳转
    handleJumpByType(btnType, btnId) {
      if (btnType === 1) {
        // 跳店铺首页
        console.log('跳店铺首页');
        openEcShop({ shopId: store.data.shopId });
      } else if (btnType === 2) {
        // 跳店铺自定义页
        console.log('跳店铺自定义页');
        plugin.openEcShopCustom({
          shopId: store.data.shopId,
          tempelateId: btnId,
        });
      } else if (btnType >= 3 && btnType <= 5) {
        // 跳转小程序
        this.navigateToMiniProgram(btnType, btnId);
      }
    },

    // 跳转到小程序
    navigateToMiniProgram(btnType, btnId) {
      const shopConfig = this.data.shopMap.find(item => item.shopId === store.data.shopId);
      if (!shopConfig) return;

      let appId = '';
      if (btnType === 3) {
        console.log('跳转小程序新客礼1.0');
        appId = shopConfig.new1Appid; // 新客礼1.0
      } else if (btnType === 4) {
        console.log('跳转小程序新客礼2.0');
        appId = shopConfig.new2Appid; // 新客礼2.0
      } else if (btnType === 5) {
        console.log('跳转小程序集罐');
        appId = shopConfig.collectTankAppid; // 集罐
      }

      if (appId) {
        tt.navigateToMiniProgram({
          appId: appId,
          path: `pages/index/index?shopId=${store.data.shopId}&activityId=${btnId}`,
        });
      }
    },
    onClose() {
      this.triggerEvent('close');
    },
  },
});
